import { useState } from "react";
import {
  createFileRoute,
  useNavigate,
  useRouter,
} from "@tanstack/react-router";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { toast } from "sonner";

import { useAuthActions } from "@renderer/stores/auth.store";
import { useMutation } from "@tanstack/react-query";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import authService, { loginSchema } from "@renderer/services/auth.service";

import { Input } from "@renderer/components/ui/input";
import { Button } from "@renderer/components/ui/button";
import { ButtonLoading } from "@renderer/components/loading/ButtonLoading";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@renderer/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@renderer/components/ui/form";

export const Route = createFileRoute("/auth/login")({
  head: () => ({
    meta: [
      {
        title: "Login",
      },
    ],
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();
  const router = useRouter();

  const { authenticate } = useAuthActions();
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const loginMutation = useMutation({
    mutationFn: authService.login,
    onSuccess: (data) => {
      authenticate(data.user, data.access_token);
      router.invalidate().then(() => navigate({ to: "/", replace: true }));
    },
    onError: (err) => toast.error(err.response.data.message),
  });

  const onSubmit = (values: z.infer<typeof loginSchema>) =>
    loginMutation.mutate(values);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Login</CardTitle>
        <CardDescription>
          Enter your email below to login to your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>username</FormLabel>
                  <FormControl>
                    <Input {...field} type="text" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type={showPassword ? "text" : "password"}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute inset-y-0 right-0 flex items-center px-3"
                        onClick={() => setShowPassword((prev) => !prev)}
                      >
                        {showPassword ? (
                          <EyeIcon
                            aria-hidden="true"
                            className="size-4 opacity-50"
                          />
                        ) : (
                          <EyeOffIcon
                            aria-hidden="true"
                            className="size-4 opacity-50"
                          />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={loginMutation.isPending}>
              {loginMutation.isPending ? <ButtonLoading /> : "Continue"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
