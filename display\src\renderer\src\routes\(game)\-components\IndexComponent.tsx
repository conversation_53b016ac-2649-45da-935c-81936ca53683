import "../../../game.css";

import { useEffect, useMemo, useRef, useState, useCallback } from "react";

// Import custom styles for history entries
import "./history-styles.css";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../../apiClient";
import { useSocketIO } from "../../../contexts/SocketIOContext";
import { useGameState } from "../GameStateContext";
// Using normal HTML tags with proper asset paths for production compatibility

import { LeftSideBar } from "./LeftSide";

// Define PRIZE_STRUCTURE at the module level
const PRIZE_STRUCTURE = {
  1: { 1: 3.8, 0: 0 },
  2: { 2: 15, 1: 0, 0: 0 },
  3: { 3: 35, 2: 3, 1: 0, 0: 0 },
  4: { 4: 100, 3: 8, 2: 1, 1: 0, 0: 0 },
  5: { 5: 300, 4: 15, 3: 3, 2: 1, 1: 0, 0: 0 },
  6: { 6: 1800, 5: 70, 4: 10, 3: 1, 2: 0, 1: 0, 0: 0 },
  7: { 7: 2150, 6: 120, 5: 12, 4: 6, 3: 1, 2: 0, 1: 0, 0: 0 },
  8: { 8: 3000, 7: 600, 6: 68, 5: 8, 4: 4, 3: 0, 2: 0, 1: 0, 0: 0 },
  9: { 9: 4200, 8: 1800, 7: 120, 6: 18, 5: 6, 4: 3, 3: 0, 2: 0, 1: 0, 0: 0 },
  10: {
    10: 5000,
    9: 2500,
    8: 400,
    7: 40,
    6: 12,
    5: 4,
    4: 2,
    3: 0,
    2: 0,
    1: 0,
    0: 0,
  },
};

// Define pick level time thresholds in seconds (updated for 4-minute countdown)
// Each pick level can now have multiple display times during the countdown
const PICK_LEVEL_TIMES = {
  10: [205, 147, 93, 40], // 3 min 0 sec, 2 min 27 sec, 1 min 37 sec, 34 sec
  9: [201, 143, 89, 36], // 2 min 55 sec, 2 min 22 sec, 1 min 32 sec, 29 sec
  8: [197, 139, 85, 32], // 2 min 50 sec, 2 min 17 sec, 1 min 27 sec, 24 sec
  7: [192, 135, 82, 29], // 2 min 45 sec, 2 min 12 sec, 1 min 22 sec, 19 sec
  6: [187, 131, 79, 25], // 2 min 40 sec, 2 min 7 sec, 1 min 17 sec, 14 sec
  5: [182, 127, 75, 22], // 2 min 35 sec, 2 min 2 sec, 1 min 12 sec, 9 sec
  4: [177, 123, 72, 19], // 2 min 30 sec, 1 min 57 sec, 1 min 7 sec, 4 sec
  3: [172, 119, 69, 15], // 2 min 25 sec, 1 min 52 sec, 1 min 2 sec
  2: [167, 115, 65, 12], // 2 min 20 sec, 1 min 47 sec, 57 sec
  1: [162, 111, 62, 9], // 2 min 15 sec, 1 min 42 sec, 52 sec
};

const TIMING = {
  INTRO_DURATION_MS: 5208.33,
  DRAW_DELAY_MS: 0,
  NUMBER_VIDEO_DURATION_MS: 2500,
  get TOTAL_DRAW_DURATION_MS() {
    return this.INTRO_DURATION_MS + 20 * this.NUMBER_VIDEO_DURATION_MS;
  },
};

type TGameStatus = {
  roundNumber?: number;
  status: "BETTING_OPEN" | "BETTING_CLOSED";
  endTime: Date;
};

type TGameHistory = {
  _id: string;
  roundNumber: number;
  drawnNumbers: number[];
};

export default function IndexComponent() {
  const { socket, isServerDown, detectPhaseTransition } = useSocketIO();
  const { gameState, setGameState } = useGameState();

  // Define game data type
  type GameData = {
    roundNumber: number;
    drawnNumbers: number[];
    timestamp: string;
  };

  // Maximum number of history entries to keep
  const MAX_HISTORY_ENTRIES = 10;

  // Store local game history in localStorage
  const storeLocalGameHistory = (history: TGameHistory[]) => {
    sessionStorage.setItem("localGameHistory2", JSON.stringify(history));
  };

  // Retrieve local game history from localStorage
  const retrieveLocalGameHistory = (): TGameHistory[] => {
    const history = sessionStorage.getItem("localGameHistory2");
    return history ? JSON.parse(history) : [];
  };

  // Determine the current pick level from state
  const [pickLevel, setPickLevel] = useState<number>(10);

  const displayablePrizes = useMemo(() => {
    const prizesForPick =
      PRIZE_STRUCTURE[pickLevel as keyof typeof PRIZE_STRUCTURE];
    if (!prizesForPick) return [];

    return Object.entries(prizesForPick)
      .map(([hits, win]) => ({ hits: parseInt(hits), win: win as number }))
      .filter((p) => p.win > 0) // Only show entries with actual prize money
      .sort((a, b) => b.hits - a.hits); // Display highest hits first
  }, [pickLevel]);

  const videoRef = useRef<HTMLVideoElement>(null);
  const introVideoRef = useRef<HTMLVideoElement>(null);

  const [gameHistory, setGameHistory] = useState<TGameHistory[]>(() => {
    // Initialize from localStorage when component mounts
    return retrieveLocalGameHistory();
  });
  const [showGameHistory, setShowGameHistory] = useState(false);

  const [gameStatus, setGameStaus] = useState<TGameStatus | undefined>(
    undefined,
  );
  const [countdown, setCountdown] = useState<number | undefined>(undefined);

  // Helper function to determine if PickLevels should be displayed at the current countdown
  const shouldShowPickLevels = useMemo(() => {
    if (countdown === undefined) return false;

    // Check if the current countdown is within range of any configured display times
    // We'll show PickLevels for a duration after each trigger time
    const DISPLAY_DURATION = 10; // Show for 10 seconds after trigger

    for (let level = 1; level <= 10; level++) {
      const times = PICK_LEVEL_TIMES[level as keyof typeof PICK_LEVEL_TIMES];
      if (times) {
        // Check if countdown is within the display window for any of this level's times
        for (const triggerTime of times) {
          const windowStart = triggerTime;
          const windowEnd = triggerTime - DISPLAY_DURATION;

          if (countdown <= windowStart && countdown > windowEnd) {
            console.log(
              `PickLevels should be displayed: countdown=${countdown}s is within window for pick level ${level} (trigger: ${triggerTime}s, window: ${windowStart}s - ${windowEnd}s)`,
            );
            return true;
          }
        }
      }
    }
    return false;
  }, [countdown]);

  // Update pick level based on countdown time
  useEffect(() => {
    if (countdown === undefined) return;

    // Find the appropriate pick level based on the countdown time
    // Check each pick level to see if the current countdown matches any of its display times
    let foundPickLevel: number | null = null;

    // Iterate through pick levels from highest to lowest
    for (let level = 10; level >= 1; level--) {
      const times = PICK_LEVEL_TIMES[level as keyof typeof PICK_LEVEL_TIMES];
      if (times && times.includes(countdown)) {
        foundPickLevel = level;
        break;
      }
    }

    // If we found a matching pick level, update it
    if (foundPickLevel !== null && foundPickLevel !== pickLevel) {
      setPickLevel(foundPickLevel);
      console.log(
        `Pick level changed to ${foundPickLevel} based on countdown: ${countdown}s (${Math.floor(countdown / 60)}m ${countdown % 60}s)`,
      );
    }
  }, [countdown, pickLevel]);

  const [allOutcomes, setAllOutcomes] = useState<number[]>([]);
  const [roundNumber, setRoundNumber] = useState<number>();
  const [offlineRoundNumber, setOfflineRoundNumber] = useState<
    number | undefined
  >();
  // Track when we first enter offline mode
  const [wasServerDown, setWasServerDown] = useState(false);

  const [outcomes, setOutcomes] = useState<number[]>([]);
  const [drawnNumber, setDrawnNumber] = useState<number>();
  const [delayedDrawnNumber, setDelayedDrawnNumber] = useState<
    number | undefined
  >(undefined);

  const [showIntroVideo, setShowIntroVideo] = useState(false);
  const [drawing, setDrawing] = useState(false);

  const [isReplayMode, setIsReplayMode] = useState(false);

  const [noDataTimer, setNoDataTimer] = useState<NodeJS.Timeout | null>(null);

  const [bettingClosedCheckTimer, setBettingClosedCheckTimer] =
    useState<NodeJS.Timeout | null>(null);

  const [lastDataReceivedTime, setLastDataReceivedTime] = useState(Date.now());

  const setupContinuousReplay = useCallback(() => {
    const REPLAY_CYCLE_DURATION = 65000;

    console.log(
      `Setting up next replay in ${REPLAY_CYCLE_DURATION / 1000} seconds`,
    );

    if (noDataTimer) {
      clearTimeout(noDataTimer);
    }
  }, [isServerDown, isReplayMode, noDataTimer]);

  const introAudioRef = useRef<HTMLAudioElement>(null);
  const countdownAudio = useRef<HTMLAudioElement>(null);
  const { data, error, isLoading } = useQuery({
    queryKey: ["gameStatus"],
    queryFn: () => apiClient("/games/status").then((res: any) => res.data),
  });
  const lastGame = useQuery({
    queryKey: ["lastGameStatus"],
    queryFn: () => apiClient("/games/game-status").then((res: any) => res.data),
  });

  useEffect(() => {
    if (data) {
      console.log("this is the data from the gameStatus", data);
      setGameStaus(data);
      convertDateToCountDown(data.endTime);
    }
    if (error) {
      console.log("this is the error");
    }
    if (isLoading) {
      console.log("this is the loading");
    }
    if (lastGame.data) {
      console.log(lastGame.data);
      const {
        drawnNumbers,
        drawTime,
        status,
        roundNumber: lastRoundNumber,
      } = lastGame.data;
      if (status === "COMPLETED" && drawnNumbers?.length && drawTime) {
        const drawTimestamp = new Date(drawTime).getTime();
        const now = Date.now();
        const elapsedMs = now - drawTimestamp;

        setAllOutcomes(drawnNumbers);
        setRoundNumber(lastRoundNumber);

        const totalDrawDuration =
          TIMING.INTRO_DURATION_MS +
          drawnNumbers.length * TIMING.NUMBER_VIDEO_DURATION_MS;

        if (elapsedMs < totalDrawDuration) {
          setDrawing(true);

          if (elapsedMs < TIMING.INTRO_DURATION_MS) {
            setShowIntroVideo(true);
            setOutcomes([]);
            setDrawnNumber(undefined);

            const introPosition = Math.max(0, elapsedMs / 1000);
            console.log(
              `API response: Setting intro position to ${introPosition} seconds`,
            );

            if (introVideoRef.current) {
              introVideoRef.current.setAttribute(
                "data-start-time",
                introPosition.toString(),
              );
            }

            if (introAudioRef.current) {
              introAudioRef.current.currentTime = 0;
              introAudioRef.current.play();
            }
          } else {
            setShowIntroVideo(false);

            const timeIntoNumberDrawing = elapsedMs - TIMING.INTRO_DURATION_MS;
            const currentNumberIndex = Math.min(
              Math.floor(
                timeIntoNumberDrawing / TIMING.NUMBER_VIDEO_DURATION_MS,
              ),
              drawnNumbers.length - 1,
            );

            setOutcomes(drawnNumbers.slice(0, currentNumberIndex));

            setDrawnNumber(drawnNumbers[currentNumberIndex]);
            setTimeout(() => {
              setOutcomes(drawnNumbers.slice(0, currentNumberIndex + 1));
            }, TIMING.NUMBER_VIDEO_DURATION_MS / 2);

            if (videoRef.current) {
              const videoPosition =
                timeIntoNumberDrawing % TIMING.NUMBER_VIDEO_DURATION_MS;
              videoRef.current.currentTime = Math.max(0, videoPosition / 1000);
              videoRef.current.play();
            }
          }
        } else {
          setDrawing(false);
          setShowIntroVideo(false);
          setOutcomes(drawnNumbers);
          setDrawnNumber(undefined);
        }
      } else if (status !== "COMPLETED") {
        setDrawing(false);
        setShowIntroVideo(false);
        setOutcomes([]);
        setAllOutcomes([]);
        setDrawnNumber(undefined);
        if (lastGame.data.roundNumber) {
          setRoundNumber(lastGame.data.roundNumber);
        }
      }
    }
  }, [data, lastGame.data]);

  // Simplified offline mode to handle server disconnections
  useEffect(() => {
    if (isServerDown) {
      console.log("Server is down - implementing simplified offline mode");

      // If this is the first time entering offline mode, set offlineRoundNumber to roundNumber + 1
      if (!wasServerDown) {
        setWasServerDown(true);
        // Only set the initial offline round number if it's not already set
        let initialOfflineRound = gameStatus?.roundNumber;
        if (!offlineRoundNumber) {
          console.log(
            `Setting initial offline round number to ${initialOfflineRound}`,
          );
          setOfflineRoundNumber(initialOfflineRound);
        }
      }

      // If countdown reaches 0, transition to BETTING_CLOSED for 5 seconds
      if (countdown === 0 && offlineRoundNumber) {
        console.log(
          "Countdown reached zero in offline mode - transitioning to BETTING_CLOSED",
        );

        // Update game status to BETTING_CLOSED
        setGameStaus({
          status: "BETTING_CLOSED",
          endTime: new Date(),
        });

        // After 5 seconds, reset the countdown to 210 seconds
        setTimeout(() => {
          console.log("Resetting countdown to 210 seconds in offline mode");

          const nextRound = offlineRoundNumber + 1;
          console.log(`Incrementing offline round number to ${nextRound}`);
          setOfflineRoundNumber(nextRound);

          // Reset to BETTING_OPEN with 210 seconds
          setGameStaus({
            status: "BETTING_OPEN",
            endTime: new Date(Date.now() + 210 * 1000),
          });

          // Update countdown
          setCountdown(210);
        }, 5000);
      }
    } else {
      // Reset wasServerDown when server comes back online
      setWasServerDown(false);
    }
  }, [isServerDown, countdown, roundNumber, offlineRoundNumber]);

  useEffect(() => {
    return () => {
      if (noDataTimer) {
        clearTimeout(noDataTimer);
      }
      if (bettingClosedCheckTimer) {
        clearTimeout(bettingClosedCheckTimer);
      }
    };
  }, [noDataTimer, bettingClosedCheckTimer]);

  useEffect(() => {
    if (!socket) return;

    const handleGameStatus = (res: TGameStatus) => {
      if (res) {
        console.log("Received game status data:", res);
        setLastDataReceivedTime(Date.now());

        if (isReplayMode) {
          console.log("Exiting replay mode due to new game status data");
          setIsReplayMode(false);
          if (noDataTimer) {
            clearTimeout(noDataTimer);
            setNoDataTimer(null);
          }

          if (bettingClosedCheckTimer) {
            clearTimeout(bettingClosedCheckTimer);
            setBettingClosedCheckTimer(null);
          }
        }

        // If we receive game status data from the server after being offline,
        // reset wasServerDown to false to indicate we're back online
        if (wasServerDown) {
          console.log(
            "Received game status from server after being offline - resuming online mode",
          );
          setWasServerDown(false);

          // If we have a roundNumber from the server, update our offlineRoundNumber to match
          if (res.roundNumber) {
            setOfflineRoundNumber(res.roundNumber);
          }
        }

        setGameStaus(res);
      }
    };

    const handleDrawnNumbers = (res: {
      drawnNumbers: number[];
      roundNumber: number;
    }) => {
      if (res) {
        setLastDataReceivedTime(Date.now());

        // Add the current game to the history array
        const newGameHistoryEntry: TGameHistory = {
          _id: Date.now().toString(), // Generate a unique ID
          roundNumber: res.roundNumber,
          drawnNumbers: res.drawnNumbers,
        };

        setGameHistory((prevHistory) => {
          const updatedHistory = [newGameHistoryEntry, ...prevHistory].slice(
            0,
            MAX_HISTORY_ENTRIES,
          );

          storeLocalGameHistory(updatedHistory);

          return updatedHistory;
        });

        // Show the game history after drawing is complete
        // setShowGameHistory(false); // Will be set to true after drawing completes

        setAllOutcomes(res.drawnNumbers);
        setRoundNumber(res.roundNumber);
        // When we receive new data from the server after being offline,
        // reset the offline round number to match the server's round number
        setOfflineRoundNumber(res.roundNumber);
        // Reset wasServerDown when we get new data from the server
        setWasServerDown(false);
        setOutcomes([]);
        setDrawnNumber(undefined);
        setShowIntroVideo(true);
        setDrawing(true);

        if (introAudioRef.current) {
          introAudioRef.current.pause();
          introAudioRef.current.currentTime = 0;
          introAudioRef.current
            .play()
            .catch((e) => console.error("Intro audio play failed:", e));
        }

        if (introVideoRef.current) {
          introVideoRef.current.currentTime = 0;
          introVideoRef.current
            .play()
            .catch((e) => console.error("Intro video play failed:", e));
        }
      }
    };

    const handleGameHistory = (res: TGameHistory[]) => {
      if (res) {
        setLastDataReceivedTime(Date.now());

        // setGameHistory((prevHistory) => {
        //   const existingGamesMap = new Map();
        //   prevHistory.forEach((game) => {
        //     existingGamesMap.set(game.roundNumber, game);
        //   });

        //   // Add server games that aren't in our local history
        //   let mergedHistory = [...prevHistory];

        //   res.forEach((serverGame) => {
        //     // Check if we already have this game in our local history
        //     if (!existingGamesMap.has(serverGame.roundNumber)) {
        //       mergedHistory.push(serverGame);
        //     }
        //   });

        //   // Sort by roundNumber in descending order (newest first)
        //   mergedHistory.sort((a, b) => b.roundNumber - a.roundNumber);

        //   // Limit to MAX_HISTORY_ENTRIES
        //   mergedHistory = mergedHistory.slice(0, MAX_HISTORY_ENTRIES);

        //   // Store the updated history in localStorage
        //   storeLocalGameHistory(mergedHistory);

        //   return mergedHistory;
        // });

        setShowGameHistory(true);
        setTimeout(() => {
          setShowGameHistory(false);
        }, 5000);
      }
    };

    socket.on("gameStatus", handleGameStatus);
    socket.on("drawnNumbers", handleDrawnNumbers);
    socket.on("gameHistory", handleGameHistory);

    return () => {
      socket.off("gameStatus", handleGameStatus);
      socket.off("drawnNumbers", handleDrawnNumbers);
      socket.off("gameHistory", handleGameHistory);

      if (noDataTimer) {
        clearTimeout(noDataTimer);
      }
      if (bettingClosedCheckTimer) {
        clearTimeout(bettingClosedCheckTimer);
      }
    };
  }, [
    socket,
    isReplayMode,
    noDataTimer,
    bettingClosedCheckTimer,
    setupContinuousReplay,
  ]);

  // This section has been removed as part of the offline simulation rewrite

  // Countdown timer effect - handles transition to zero and triggers replay when needed
  useEffect(() => {
    // If no endTime or server is down, don't run the countdown timer
    if (!gameStatus?.endTime) {
      setCountdown(0);
      return;
    }

    console.log(
      `Setting up countdown timer for endTime: ${gameStatus.endTime}`,
    );

    let intervalTimer: NodeJS.Timeout | null = null;

    const startTimer = () => {
      intervalTimer = setInterval(() => {
        if (!gameStatus?.endTime) {
          if (intervalTimer) clearInterval(intervalTimer);
          setCountdown(0);
          return;
        }

        const endTimestamp = new Date(gameStatus.endTime).getTime();
        const remaining = Math.max(
          0,
          Math.round((endTimestamp - Date.now()) / 1000),
        );

        // Update the countdown
        setCountdown(remaining);

        // Detect phase transitions for the socket context
        if (detectPhaseTransition) {
          detectPhaseTransition(gameStatus.status, remaining);
        }

        // Update countdown value
      }, 100);
    };

    startTimer();

    return () => {
      if (intervalTimer) {
        clearInterval(intervalTimer);
      }
    };
  }, [
    gameStatus?.endTime,
    gameStatus?.status,
    lastDataReceivedTime,
    isReplayMode,
    drawing,
    showIntroVideo,
    isServerDown,
  ]);

  // useEffect(() => {
  //   if (!lastSuccessfulGame || isServerDown) return;

  //   const checkForReplayMode = () => {
  //     const currentTime = Date.now();
  //     const timeSinceLastData = currentTime - lastDataReceivedTime;

  //     const countdownZero = countdown === 0;
  //     const bettingClosed = gameStatus?.status === "BETTING_CLOSED";

  //     if (
  //       (countdownZero || bettingClosed) &&
  //       !isReplayMode &&
  //       !drawing &&
  //       !showIntroVideo
  //     ) {
  //       console.log(
  //         `Replay triggered: Countdown: ${countdown}, Status: ${gameStatus?.status}, Time since last data: ${timeSinceLastData}ms`,
  //       );

  //       // replayLastGame();

  //       const setupContinuousReplay = () => {
  //         const TOTAL_GAME_DURATION = 210000;

  //         const replayAgainTimer = setTimeout(() => {
  //           if (!isServerDown && isReplayMode) {
  //             console.log("Continuing replay cycle - replaying again");
  //             replayLastGame();

  //             setupContinuousReplay();
  //           }
  //         }, TOTAL_GAME_DURATION);

  //         setNoDataTimer(replayAgainTimer);
  //       };

  //       setupContinuousReplay();
  //     } else if (
  //       timeSinceLastData > 30000 &&
  //       !isReplayMode &&
  //       !drawing &&
  //       !showIntroVideo
  //     ) {
  //       console.log("No data received for 30 seconds, entering replay mode");
  //       replayLastGame();

  //       const replayAgainTimer = setTimeout(() => {
  //         if (!isServerDown && isReplayMode) {
  //           console.log("Still no new data, replaying again");
  //           replayLastGame();
  //         }
  //       }, TIMING.TOTAL_DRAW_DURATION_MS + 5000);

  //       setNoDataTimer(replayAgainTimer);
  //     }
  //   };

  //   const checkTimer = setInterval(checkForReplayMode, 1000);

  //   return () => {
  //     clearInterval(checkTimer);
  //   };
  // }, [
  //   lastSuccessfulGame,
  //   isServerDown,
  //   isReplayMode,
  //   lastDataReceivedTime,
  //   drawing,
  //   showIntroVideo,
  //   replayLastGame,
  //   countdown,
  //   gameStatus?.status,
  // ]);

  // Effect to handle BETTING_CLOSED state and trigger replay if needed

  useEffect(() => {
    if (!showIntroVideo || !introVideoRef.current || !lastGame.data?.drawTime)
      return;

    const drawTimestamp = new Date(lastGame.data.drawTime).getTime();
    const now = Date.now();
    const elapsedMs = now - drawTimestamp;

    if (elapsedMs < TIMING.INTRO_DURATION_MS) {
      const introPosition = Math.max(0, elapsedMs / 1000);

      console.log(
        `Effect: Setting intro video position to ${introPosition} seconds`,
      );

      const setVideoPosition = () => {
        if (introVideoRef.current) {
          if (introVideoRef.current.readyState < 2) {
            introVideoRef.current.load();
          }

          introVideoRef.current.currentTime = introPosition;

          const playPromise = introVideoRef.current.play();
          if (playPromise !== undefined) {
            playPromise.catch((error) => {
              console.log("Auto-play prevented:", error);
            });
          }
        }
      };

      setVideoPosition();

      const timer1 = setTimeout(setVideoPosition, 100);

      const timer2 = setTimeout(setVideoPosition, 500);

      const intervalTimer = setInterval(() => {
        if (introVideoRef.current) {
          const currentPosition = introVideoRef.current.currentTime;
          const expectedPosition = (Date.now() - drawTimestamp) / 1000;

          if (
            Math.abs(currentPosition - expectedPosition) > 0.5 &&
            expectedPosition < TIMING.INTRO_DURATION_MS / 1000
          ) {
            console.log(
              `Correcting video position: current=${currentPosition}, expected=${expectedPosition}`,
            );
            introVideoRef.current.currentTime = expectedPosition;
          }
        }
      }, 200);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearInterval(intervalTimer);
      };
    }
  }, [showIntroVideo, lastGame.data?.drawTime]);

  useEffect(() => {
    const introVideo = introVideoRef.current;
    const numberVideo = videoRef.current;

    if (showIntroVideo && introVideo) {
      console.log("Effect: Playing intro video");

      introVideo
        .play()
        .catch((e) => console.error("Intro video play failed:", e));
      if (numberVideo) numberVideo.pause();
    } else if (
      drawing &&
      !showIntroVideo &&
      delayedDrawnNumber &&
      numberVideo
    ) {
      console.log(`Effect: Playing video for number ${delayedDrawnNumber}`);

      numberVideo.currentTime = 0;
      numberVideo
        .play()
        .catch((e) => console.error("Number video play failed:", e));
      if (introVideo) introVideo.pause();
    } else {
      if (introVideo) introVideo.pause();
      if (numberVideo) numberVideo.pause();
    }
  }, [showIntroVideo, drawing, delayedDrawnNumber]);

  useEffect(() => {
    if (!drawing || !allOutcomes.length) {
      return;
    }

    let drawTimer: NodeJS.Timeout | null = null;
    let currentNumberIndex = -1;

    const drawNextNumber = () => {
      currentNumberIndex++;

      if (currentNumberIndex < allOutcomes.length) {
        const nextNumber = allOutcomes[currentNumberIndex];
        console.log(`Drawing number ${currentNumberIndex + 1}: ${nextNumber}`);

        setDrawnNumber(nextNumber);

        const revealDelay = TIMING.NUMBER_VIDEO_DURATION_MS / 2;
        setTimeout(() => {
          setOutcomes((prevOutcomes) => {
            if (drawing && !prevOutcomes.includes(nextNumber)) {
              return [...prevOutcomes, nextNumber];
            }
            return prevOutcomes;
          });
        }, revealDelay);

        drawTimer = setTimeout(drawNextNumber, TIMING.NUMBER_VIDEO_DURATION_MS);
      } else {
        console.log("Drawing sequence complete.");
        setDrawing(false);
        setDrawnNumber(undefined);
        setShowIntroVideo(false);

        // Show game history after drawing is complete
        setShowGameHistory(true);

        // We'll display our local history instead of waiting for server response
        console.log("Draw finished, displaying local game history");

        // Optionally request server history to keep our local history in sync,
        // but we don't need to wait for the response to show history
        if (socket) {
          console.log("Requesting recent games from server to sync");
          socket.emit("getRecentGames");
        }
      }
    };

    if (showIntroVideo) {
      console.log("Waiting for intro video to complete...");
      drawTimer = setTimeout(() => {
        if (drawing) {
          console.log("Intro finished, starting number draw.");
          setShowIntroVideo(false);
          drawNextNumber();
        }
      }, TIMING.INTRO_DURATION_MS);
    } else {
      const lastRevealedNumber =
        outcomes.length > 0 ? outcomes[outcomes.length - 1] : undefined;
      currentNumberIndex = lastRevealedNumber
        ? allOutcomes.indexOf(lastRevealedNumber)
        : -1;

      console.log(
        `Drawing effect started without intro. Resuming after index: ${currentNumberIndex}`,
      );

      if (drawnNumber) {
        const drawnNumberIndex = allOutcomes.indexOf(drawnNumber);
        if (drawnNumberIndex !== -1) {
          currentNumberIndex = drawnNumberIndex;

          console.log(
            `Resuming: ${drawnNumber} is active. Scheduling next draw.`,
          );
          drawTimer = setTimeout(
            drawNextNumber,
            TIMING.NUMBER_VIDEO_DURATION_MS,
          );
        } else {
          console.warn("drawnNumber not found in allOutcomes during resume.");
          drawNextNumber();
        }
      } else {
        console.log("Resuming: No number active. Starting draw sequence.");
        drawNextNumber();
      }
    }

    return () => {
      if (drawTimer) {
        clearTimeout(drawTimer);
      }
    };
  }, [drawing, allOutcomes, showIntroVideo, socket]);

  function convertDateToCountDown(endTime: Date) {
    const closeTime = new Date(endTime).getTime();
    const timeRemaining = Math.max(
      0,
      Math.round((closeTime - Date.now()) / 1000),
    );
    setCountdown(timeRemaining);
  }

  const [minute, second] = useMemo(() => {
    if (countdown === undefined) return [0, 0];
    return [Math.floor(countdown / 60), countdown % 60];
    // return [1, 45];
    // return [2, 34];
  }, [countdown]);

  // Define delayed outcomes for LeftSideBar with a 1-second delay
  const [delayedOutcomes, setDelayedOutcomes] = useState<number[]>([]);

  // Effect to update delayedOutcomes with a delay when outcomes change
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedOutcomes([...outcomes]);
    }, 100); // 200ms delay

    return () => clearTimeout(timer);
  }, [outcomes]);

  // Effect to persist game history to localStorage whenever it changes
  useEffect(() => {
    if (gameHistory.length > 0) {
      storeLocalGameHistory(gameHistory);
    }
  }, [gameHistory]);

  // Effect to update delayedDrawnNumber with a 1-second delay when drawnNumber changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedDrawnNumber(drawnNumber);
    }, 500); // 1 second delay

    return () => clearTimeout(timer);
  }, [drawnNumber]);

  const hourNumber = (num: string | number) => {
    return (
      <img
        crossOrigin="anonymous"
        className="HourNumberImage"
        src={`images/${num}.png`}
        alt={`Number ${num}`}
      />
    );
  };

  const MultipleLevels = () => (
    <div style={{ fontSize: "4.4vw" }} className="mt-10 text-center">
      <div className="text-error Goodtimes">Pick 3</div>
      <div className="Goodtimes -my-10">to</div>
      <div className="text-error Goodtimes -py-10">Pick 10</div>
      <div className="-py-10 -my-5 font-semibold" style={{ fontSize: "4vw" }}>
        games have
      </div>
      <div
        className="Goodtimes -mb-10 text-[#f3f300]"
        style={{ fontSize: "5vw" }}
      >
        Multiple
      </div>
      <span
        className="Goodtimes -mb-10 text-[#f3f300]"
        style={{ fontSize: "5vw" }}
      >
        Pay Levels
      </span>
      <div className="" style={{ fontSize: "4vw" }}>
        On other sports
      </div>
    </div>
  );
  const TwentyBalls = () => (
    <div style={{ fontSize: "4vw" }} className="Goodtimes mt-10 text-center">
      <span className="text-error">20</span> balls <br /> Drawn <br /> from{" "}
      <span className="text-error">80</span>
    </div>
  );
  const PickLevels = () => (
    <div
      className="prize-table-container"
      style={{
        width: "100%",
        marginTop: "1rem",
      }}
    >
      <div
        style={{
          textAlign: "center",
          fontSize: "4.3vw",
        }}
        className="Goodtimes text-error"
      >
        PICK {pickLevel}
      </div>
      <div
        style={{
          fontSize: "4vw",
        }}
        className="mx-[15%] grid grid-cols-2"
      >
        <div style={{ color: "#f3f300", fontWeight: "bold" }}>HITS</div>
        <div
          style={{
            color: "#f3f300",
            fontWeight: "bold",
            // minWidth: "150px",
          }}
        >
          WIN
        </div>
      </div>
      {displayablePrizes.map((item, i) => {
        if (i === 0 && item.hits > 6) return null;
        return (
          <div
            key={item.hits}
            style={{
              color: "white",
              fontSize: "2.7vw",
              // gap: "",
            }}
            className="mx-[15%] grid grid-cols-2"
          >
            <div>{item.hits}</div>
            <div>{item.win.toLocaleString()}</div>
          </div>
        );
      })}
    </div>
  );

  const PickTen = () => (
    <div style={{ fontSize: "3vw" }} className="Goodtimes mt-10 text-center">
      Play <br /> The <span className="text-error">PICK 10</span> Game <br />{" "}
      <br />
      Get <span className="text-error">10</span> numbers <br /> correct, and{" "}
      <br /> win the <br />
      <br /> <span className="text-error">PICK 10</span> JACKPOT
    </div>
  );
  const OneToTen = () => (
    <div style={{ fontSize: "4vw" }} className="Goodtimes mt-10 text-center">
      pick <span className="text-error">1</span> to{" "}
      <span className="text-error">10</span> <br /> numbers <br /> from{" "}
      <span className="text-error">80</span>
    </div>
  );

  return (
    <>
      <div className="App">
        {!drawing && !showIntroVideo && !showGameHistory && (
          <audio
            crossOrigin="anonymous"
            ref={introAudioRef}
            autoPlay
            loop
            src="sounds/1intro.mp3"
          />
        )}
        {countdown && countdown <= 10 ? (
          <audio
            crossOrigin="anonymous"
            ref={countdownAudio}
            autoPlay
            loop
            src="sounds/countdownAudio.mp3"
          />
        ) : null}
        {!showIntroVideo && !drawing && !showGameHistory && (
          <div className="NumberAppContainer">
            <div className="leftSideBar">
              <LeftSideBar
                type="static"
                outcomes={outcomes}
                roundNumber={roundNumber}
              />
            </div>
            <div className="info flex flex-col items-center">
              <div className="text-darkOrange Eurostib flex items-center justify-between uppercase">
                <div>
                  <img
                    crossOrigin="anonymous"
                    className="drawTextImage"
                    src="images/9draw.png"
                    alt="Draw text"
                  />
                </div>
                <p
                  style={{
                    fontSize: "4.5vw",
                    fontFamily: "Eurostib",
                    lineHeight: 1,
                    fontWeight: 900,
                  }}
                  className="text-shadow text-white"
                >
                  {isServerDown
                    ? offlineRoundNumber
                    : gameStatus?.roundNumber || roundNumber || 1}
                </p>
              </div>
              <div
                className={`mt-10 flex ${
                  countdown !== undefined &&
                  countdown < 10 &&
                  gameStatus?.status !== "BETTING_CLOSED"
                    ? "blink opacity-0 transition-opacity"
                    : "opacity-100 transition-opacity duration-1000"
                }`}
              >
                {minute !== undefined && second !== undefined && (
                  <>
                    {gameStatus?.status === "BETTING_CLOSED" ? (
                      <img
                        crossOrigin="anonymous"
                        src="images/9betsclosed.png"
                        style={{ height: "4vw" }}
                        alt="Bets closed"
                      />
                    ) : (
                      <>
                        {minute < 10 ? (
                          <>
                            {hourNumber(0)} {hourNumber(minute)}
                          </>
                        ) : (
                          <>
                            {hourNumber(String(minute).split("")[0])}{" "}
                            {hourNumber(String(minute).split("")[1])}
                          </>
                        )}
                        {hourNumber("9colon")}
                        {second < 10 ? (
                          <>
                            {hourNumber(0)}
                            {hourNumber(second)}
                          </>
                        ) : (
                          <>
                            {hourNumber(String(second).split("")[0])}{" "}
                            {hourNumber(String(second).split("")[1])}
                          </>
                        )}
                      </>
                    )}
                  </>
                )}
              </div>
              {minute === 3 && second >= 25 && second <= 30 && <TwentyBalls />}
              {minute === 3 && second > 30 && second <= 34 && <TwentyBalls />}
              {minute === 2 && second > 36 && second < 42 && <TwentyBalls />}
              {minute === 2 && second > 31 && second <= 36 && <PickTen />}
              {minute === 2 && second <= 31 && second > 27 && (
                <MultipleLevels />
              )}
              {(minute === 2 && second >= 42) ||
              (minute === 3 && second < 25) ? (
                <PickLevels />
              ) : null}
              {(minute === 2 && second <= 27) ||
              (minute === 1 && second > 52) ? (
                <PickLevels />
              ) : null}
              {(minute === 1 && second < 35) ||
              (minute === 0 && second > 59) ? (
                <PickLevels />
              ) : null}
              {minute === 0 && second < 41 && second > 5 ? (
                <PickLevels />
              ) : null}
              {minute === 1 && second <= 52 && second > 47 && <OneToTen />}
              {minute === 1 && second <= 47 && second > 43 && <TwentyBalls />}
              {minute === 1 && second <= 43 && second > 38 && <PickTen />}
              {minute === 1 && second <= 38 && second > 34 && (
                <MultipleLevels />
              )}
              {minute === 0 && second <= 59 && second > 54 && <OneToTen />}
              {minute === 0 && second <= 54 && second > 49 && <TwentyBalls />}
              {minute === 0 && second <= 49 && second > 44 && <PickTen />}
              {minute === 0 && second <= 44 && second > 40 && (
                <MultipleLevels />
              )}
              {minute === 0 && second <= 5 && second > 1 && <OneToTen />}
              {minute === 0 && second <= 1 && <TwentyBalls />}
            </div>
          </div>
        )}
        {!showIntroVideo && !showGameHistory && drawing && (
          <div className="NumberAppContainer">
            <div className="leftSideBar">
              <LeftSideBar
                type={"drawing"}
                outcomes={delayedOutcomes}
                roundNumber={roundNumber}
              />
            </div>
            <div className="drawingVideoContainer">
              <div className="drawenNumbers text-shadow z-50 text-[4.5rem]">
                <sup>{outcomes.length}</sup>
                <span className="text-gray-300/80">/</span>
                <sub>{allOutcomes.length}</sub>
              </div>
              {allOutcomes.map((outcome, i) => {
                if (outcome === delayedDrawnNumber) {
                  return (
                    <video
                      crossOrigin="anonymous"
                      key={`video-${outcome}-${i}`}
                      ref={videoRef}
                      poster="images/placeholder.png"
                      src={`videos/${outcome}.mp4`}
                      autoPlay
                      playsInline
                      preload="auto"
                      className="absolute inset-0 h-full w-full border-none object-cover outline-none"
                      style={{ opacity: 1 }}
                    />
                  );
                }
                return null;
              })}
            </div>
          </div>
        )}
        {/* {showGameHistory && ( */}
        {!showIntroVideo && !drawing && showGameHistory && (
          <div className="history Eurostib p-16">
            {gameHistory.map((game) => (
              <div
                key={game._id}
                className="history-entry"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "1rem",
                  width: "100%",
                }}
              >
                <p
                  style={{
                    fontSize: "3.5rem",
                    fontWeight: "bold",
                    color: "white",
                  }}
                >
                  {game.roundNumber}
                </p>
                <div className="middle-line grid grid-cols-20 gap-2">
                  {game.drawnNumbers
                    .sort((a, b) => a - b)
                    .map((outcome, i) => (
                      <div className="flex">
                        <div
                          key={outcome}
                          style={{
                            backgroundColor:
                              outcome > 40 ? "#fe940d" : "#fef109",
                            color: "black",
                          }}
                          className="grid size-[4.6rem] place-items-center rounded-full text-[2.8rem] font-bold"
                        >
                          {outcome}
                        </div>
                        {i === 9 ? (
                          <div className="h-full w-1 place-items-center bg-red-500 opacity-50"></div>
                        ) : null}
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {showIntroVideo && (
          <div className="relative">
            <div className="absolute top-[2.5rem] left-[3.5vw] flex items-center justify-between uppercase">
              <div>
                <img
                  crossOrigin="anonymous"
                  className="drawTextImage"
                  src="images/9draw.png"
                  alt="Draw text"
                />
              </div>
              <p
                style={{
                  fontSize: "4.5vw",
                  fontFamily: "Eurostib",
                  lineHeight: 1,
                  fontWeight: 900,
                }}
                className="text-shadow text-white"
              >
                {roundNumber}
              </p>
            </div>
            <video
              crossOrigin="anonymous"
              poster="images/placeholder.png"
              src="videos/z4.mp4"
              autoPlay
              onEnded={() => setShowIntroVideo(false)}
              className="size-full object-cover"
            />
            <img
              crossOrigin="anonymous"
              src="images/app-logo.png"
              alt="app logo"
              className="absolute bottom-1 left-6 z-50 h-50 w-auto object-cover"
            />
          </div>
        )}
      </div>
    </>
  );
}
